{% extends "admin/base.html" %}

{% block title %}إيصال الدفع {{ payment.payment_number }} - {{ super() }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">
            <i class="fas fa-receipt {{ 'ml-3' if current_lang == 'ar' else 'mr-3' }}"></i>
            إيصال الدفع
        </h1>
        <div class="flex space-x-2 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
            <a href="{{ url_for('admin.pos_print_receipt', payment_id=payment.id) }}" 
               target="_blank"
               class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-print {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                طباعة الإيصال
            </a>
            <a href="{{ url_for('admin.order_detail', id=payment.order.id) }}" 
               class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                <i class="fas fa-arrow-left {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                العودة للطلب
            </a>
        </div>
    </div>

    <!-- Success Message -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
            <div class="{{ 'mr-3' if current_lang == 'ar' else 'ml-3' }}">
                <h3 class="text-lg font-medium text-green-800">
                    تم معالجة الدفع بنجاح
                </h3>
                <p class="text-green-600">
                    تم تسجيل الدفع وتحديث حالة الطلب
                </p>
            </div>
        </div>
    </div>

    <!-- Receipt -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-8 text-center">
            <h2 class="text-2xl font-bold mb-2">{{ settings.shop_name }}</h2>
            <p class="text-blue-100">إيصال دفع</p>
        </div>

        <div class="p-8">
            <!-- Receipt Info -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">معلومات الإيصال</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">رقم الإيصال:</span>
                            <span class="font-medium">{{ payment.payment_number }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">التاريخ:</span>
                            <span class="font-medium">{{ payment.payment_timestamp.strftime('%Y-%m-%d') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الوقت:</span>
                            <span class="font-medium">{{ payment.payment_timestamp.strftime('%H:%M:%S') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الكاشير:</span>
                            <span class="font-medium">{{ payment.processed_by.username }}</span>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">معلومات الطلب</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">رقم الطلب:</span>
                            <span class="font-medium">{{ payment.order.order_number }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">العميل:</span>
                            <span class="font-medium">{{ payment.order.customer_name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الهاتف:</span>
                            <span class="font-medium">{{ payment.order.customer_phone }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الطاولة:</span>
                            <span class="font-medium">طاولة {{ payment.order.table.table_number }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">عناصر الطلب</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 {{ 'text-right' if current_lang == 'ar' else 'text-left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">الصنف</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">الكمية</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">سعر الوحدة</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">المجموع</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for item in payment.order.order_items %}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ item.product.get_name('ar') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                                        {{ item.quantity }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                                        {{ item.unit_price }} {{ payment.order.currency }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                                        {{ item.total_price }} {{ payment.order.currency }}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Payment Summary -->
            <div class="border-t border-gray-200 pt-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">معلومات الدفع</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">طريقة الدفع:</span>
                                <span class="font-medium">{{ payment.get_payment_method_text('ar') }}</span>
                            </div>
                            {% if payment.reference_number %}
                                <div class="flex justify-between">
                                    <span class="text-gray-600">رقم المرجع:</span>
                                    <span class="font-medium">{{ payment.reference_number }}</span>
                                </div>
                            {% endif %}
                            <div class="flex justify-between">
                                <span class="text-gray-600">حالة الدفع:</span>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    {% if payment.get_status_color() == 'green' %}bg-green-100 text-green-800
                                    {% elif payment.get_status_color() == 'red' %}bg-red-100 text-red-800
                                    {% elif payment.get_status_color() == 'yellow' %}bg-yellow-100 text-yellow-800
                                    {% elif payment.get_status_color() == 'blue' %}bg-blue-100 text-blue-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ payment.get_status_text('ar') }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">ملخص المبالغ</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">المجموع الفرعي:</span>
                                <span class="font-medium">{{ payment.order.total_amount }} {{ payment.order.currency }}</span>
                            </div>
                            <div class="flex justify-between text-lg font-bold border-t border-gray-200 pt-2">
                                <span>المجموع الكلي:</span>
                                <span class="text-green-600">{{ payment.order.total_amount }} {{ payment.order.currency }}</span>
                            </div>
                            <div class="flex justify-between text-lg font-bold text-blue-600">
                                <span>المبلغ المدفوع:</span>
                                <span>{{ payment.get_formatted_amount() }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notes -->
            {% if payment.notes %}
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">ملاحظات</h3>
                    <p class="text-gray-600">{{ payment.notes }}</p>
                </div>
            {% endif %}

            <!-- Footer -->
            <div class="mt-8 pt-6 border-t border-gray-200 text-center text-gray-500">
                <p class="mb-2">شكراً لزيارتكم</p>
                <p class="text-sm">نتطلع لخدمتكم مرة أخرى</p>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="mt-6 flex justify-center space-x-4 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
        <a href="{{ url_for('admin.pos_dashboard') }}" 
           class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors">
            <i class="fas fa-home {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
            العودة لنقاط البيع
        </a>
        
        {% if payment.payment_status == 'completed' and payment.amount > 0 %}
            <a href="{{ url_for('admin.pos_process_refund', payment_id=payment.id) }}" 
               class="bg-red-500 text-white px-6 py-2 rounded-lg hover:bg-red-600 transition-colors">
                <i class="fas fa-undo {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                استرداد
            </a>
        {% endif %}
    </div>
</div>
{% endblock %}
