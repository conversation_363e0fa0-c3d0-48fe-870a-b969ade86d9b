{% extends "admin/base.html" %}

{% block title %}إدارة الطلبات - {{ super() }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">
            <i class="fas fa-receipt {{ 'ml-3' if current_lang == 'ar' else 'mr-3' }}"></i>
            إدارة الطلبات
        </h1>
        <div class="flex space-x-2 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
            <a href="{{ url_for('admin.tables') }}" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-chair {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                إدارة الطاولات
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-blue-500">
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">{{ order_stats.total }}</div>
                <div class="text-sm text-gray-600">إجمالي الطلبات</div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-yellow-500">
            <div class="text-center">
                <div class="text-2xl font-bold text-yellow-600">{{ order_stats.pending }}</div>
                <div class="text-sm text-gray-600">في الانتظار</div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-indigo-500">
            <div class="text-center">
                <div class="text-2xl font-bold text-indigo-600">{{ order_stats.preparing }}</div>
                <div class="text-sm text-gray-600">قيد التحضير</div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-green-500">
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600">{{ order_stats.ready }}</div>
                <div class="text-sm text-gray-600">جاهز</div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-gray-500">
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-600">{{ order_stats.served }}</div>
                <div class="text-sm text-gray-600">تم التقديم</div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-gray-800">
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-800">{{ order_stats.completed }}</div>
                <div class="text-sm text-gray-600">مكتمل</div>
            </div>
        </div>
    </div>

    <!-- Filter Buttons -->
    <div class="bg-white rounded-lg shadow-md p-4 mb-6">
        <div class="flex flex-wrap gap-2">
            <a href="{{ url_for('admin.orders') }}"
               class="px-4 py-2 rounded-lg transition-colors {{ 'bg-blue-500 text-white' if not current_status else 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                جميع الطلبات
            </a>
            <a href="{{ url_for('admin.orders', status='pending') }}"
               class="px-4 py-2 rounded-lg transition-colors {{ 'bg-yellow-500 text-white' if current_status == 'pending' else 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                في الانتظار
            </a>
            <a href="{{ url_for('admin.orders', status='preparing') }}"
               class="px-4 py-2 rounded-lg transition-colors {{ 'bg-indigo-500 text-white' if current_status == 'preparing' else 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                قيد التحضير
            </a>
            <a href="{{ url_for('admin.orders', status='ready') }}"
               class="px-4 py-2 rounded-lg transition-colors {{ 'bg-green-500 text-white' if current_status == 'ready' else 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                جاهز للتقديم
            </a>
            <a href="{{ url_for('admin.orders', status='served') }}"
               class="px-4 py-2 rounded-lg transition-colors {{ 'bg-gray-500 text-white' if current_status == 'served' else 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                تم التقديم
            </a>
            <a href="{{ url_for('admin.orders', status='completed') }}"
               class="px-4 py-2 rounded-lg transition-colors {{ 'bg-gray-800 text-white' if current_status == 'completed' else 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                مكتمل
            </a>
            <a href="{{ url_for('admin.orders', status='cancelled') }}"
               class="px-4 py-2 rounded-lg transition-colors {{ 'bg-red-500 text-white' if current_status == 'cancelled' else 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                ملغي
            </a>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-list {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                قائمة الطلبات
                {% if current_status %}
                    - {{ current_status }}
                {% endif %}
            </h3>
        </div>
        <div class="overflow-x-auto">
            {% if orders.items %}
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 {{ 'text-right' if current_lang == 'ar' else 'text-left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الطلب</th>
                            <th class="px-6 py-3 {{ 'text-right' if current_lang == 'ar' else 'text-left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">العميل</th>
                            <th class="px-6 py-3 {{ 'text-right' if current_lang == 'ar' else 'text-left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">الطاولة</th>
                            <th class="px-6 py-3 {{ 'text-right' if current_lang == 'ar' else 'text-left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 {{ 'text-right' if current_lang == 'ar' else 'text-left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">المجموع</th>
                            <th class="px-6 py-3 {{ 'text-right' if current_lang == 'ar' else 'text-left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                            <th class="px-6 py-3 {{ 'text-right' if current_lang == 'ar' else 'text-left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for order in orders.items %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-blue-600">{{ order.order_number }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ order.customer_name }}</div>
                                        <div class="text-sm text-gray-500">{{ order.customer_phone }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                        طاولة {{ order.table.table_number }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-{{ 'yellow-100 text-yellow-800' if order.get_status_color() == 'yellow' else 'green-100 text-green-800' if order.get_status_color() == 'green' else 'red-100 text-red-800' if order.get_status_color() == 'red' else 'blue-100 text-blue-800' if order.get_status_color() == 'blue' else 'gray-100 text-gray-800' }}">
                                        {{ order.get_status_text('ar') }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-green-600">{{ order.get_formatted_total() }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
                                        <a href="{{ url_for('admin.order_detail', id=order.id) }}"
                                           class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('admin.edit_order', id=order.id) }}"
                                           class="text-gray-600 hover:text-gray-900">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="{{ url_for('admin.delete_order', id=order.id) }}"
                                              class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الطلب؟')">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                            <button type="submit" class="text-red-600 hover:text-red-900">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>

            <!-- Pagination -->
            {% if orders.pages > 1 %}
                <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                    <div class="flex items-center justify-between">
                        <div class="flex-1 flex justify-between sm:hidden">
                            {% if orders.has_prev %}
                                <a href="{{ url_for('admin.orders', page=orders.prev_num, status=current_status) }}"
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    السابق
                                </a>
                            {% endif %}
                            {% if orders.has_next %}
                                <a href="{{ url_for('admin.orders', page=orders.next_num, status=current_status) }}"
                                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    التالي
                                </a>
                            {% endif %}
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    عرض <span class="font-medium">{{ (orders.page - 1) * orders.per_page + 1 }}</span>
                                    إلى <span class="font-medium">{{ orders.page * orders.per_page if orders.page * orders.per_page < orders.total else orders.total }}</span>
                                    من <span class="font-medium">{{ orders.total }}</span> نتيجة
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    {% if orders.has_prev %}
                                        <a href="{{ url_for('admin.orders', page=orders.prev_num, status=current_status) }}"
                                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            السابق
                                        </a>
                                    {% endif %}

                                    {% for page_num in orders.iter_pages() %}
                                        {% if page_num %}
                                            {% if page_num != orders.page %}
                                                <a href="{{ url_for('admin.orders', page=page_num, status=current_status) }}"
                                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                                    {{ page_num }}
                                                </a>
                                            {% else %}
                                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                                    {{ page_num }}
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                                …
                                            </span>
                                        {% endif %}
                                    {% endfor %}

                                    {% if orders.has_next %}
                                        <a href="{{ url_for('admin.orders', page=orders.next_num, status=current_status) }}"
                                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            التالي
                                        </a>
                                    {% endif %}
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-12">
                <i class="fas fa-receipt text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">لا توجد طلبات</h3>
                <p class="text-gray-600">
                    {% if current_status %}
                        لا توجد طلبات بحالة "{{ current_status }}"
                    {% else %}
                        لم يتم تقديم أي طلبات بعد
                    {% endif %}
                </p>
            </div>
        {% endif %}
        </div>
    </div>
</div>
{% endblock %}
