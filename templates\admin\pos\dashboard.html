{% extends "admin/base.html" %}

{% block title %}نظام نقاط البيع - {{ super() }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">
            <i class="fas fa-cash-register {{ 'ml-3' if current_lang == 'ar' else 'mr-3' }}"></i>
            نظام نقاط البيع
        </h1>
        <div class="flex space-x-2 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
            {% if active_shift %}
                <a href="{{ url_for('admin.pos_close_shift') }}" class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                    <i class="fas fa-stop {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                    إغلاق الوردية
                </a>
            {% else %}
                <a href="{{ url_for('admin.pos_start_shift') }}" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                    <i class="fas fa-play {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                    بدء وردية جديدة
                </a>
            {% endif %}
            <a href="{{ url_for('admin.pos_cash_management') }}" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-wallet {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                إدارة النقد
            </a>
        </div>
    </div>

    <!-- Active Shift Info -->
    {% if active_shift %}
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-clock text-green-600 text-xl"></i>
                </div>
                <div class="{{ 'mr-3' if current_lang == 'ar' else 'ml-3' }}">
                    <h3 class="text-lg font-medium text-green-800">
                        الوردية النشطة - {{ active_shift.shift_number }}
                    </h3>
                    <p class="text-green-600">
                        بدأت في: {{ active_shift.start_time.strftime('%Y-%m-%d %H:%M') }} | 
                        المدة: {{ active_shift.get_duration() }} |
                        الرصيد الافتتاحي: {{ active_shift.get_formatted_opening_balance() }}
                    </p>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Today's Orders -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-shopping-cart text-blue-600 text-2xl"></i>
                </div>
                <div class="{{ 'mr-4' if current_lang == 'ar' else 'ml-4' }}">
                    <p class="text-sm font-medium text-gray-500">طلبات اليوم</p>
                    <p class="text-2xl font-bold text-gray-900">{{ today_orders }}</p>
                </div>
            </div>
        </div>

        <!-- Today's Payments -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-credit-card text-green-600 text-2xl"></i>
                </div>
                <div class="{{ 'mr-4' if current_lang == 'ar' else 'ml-4' }}">
                    <p class="text-sm font-medium text-gray-500">مدفوعات اليوم</p>
                    <p class="text-2xl font-bold text-gray-900">{{ today_payments }}</p>
                </div>
            </div>
        </div>

        <!-- Today's Revenue -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-chart-line text-purple-600 text-2xl"></i>
                </div>
                <div class="{{ 'mr-4' if current_lang == 'ar' else 'ml-4' }}">
                    <p class="text-sm font-medium text-gray-500">إيرادات اليوم</p>
                    <p class="text-2xl font-bold text-gray-900">{{ today_revenue }} {{ settings.default_currency }}</p>
                </div>
            </div>
        </div>

        <!-- Cash Balance -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-wallet text-yellow-600 text-2xl"></i>
                </div>
                <div class="{{ 'mr-4' if current_lang == 'ar' else 'ml-4' }}">
                    <p class="text-sm font-medium text-gray-500">رصيد الخزنة</p>
                    <p class="text-2xl font-bold text-gray-900">{{ cash_drawer.get_formatted_balance() }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Orders -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-clock {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                الطلبات المعلقة للدفع
            </h3>
        </div>
        
        {% if pending_orders %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 {{ 'text-right' if current_lang == 'ar' else 'text-left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الطلب</th>
                            <th class="px-6 py-3 {{ 'text-right' if current_lang == 'ar' else 'text-left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">العميل</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">الطاولة</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for order in pending_orders %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ order.order_number }}</div>
                                    <div class="text-sm text-gray-500">{{ order.created_at.strftime('%H:%M') }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ order.customer_name }}</div>
                                    <div class="text-sm text-gray-500">{{ order.customer_phone }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                        طاولة {{ order.table.table_number }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium text-gray-900">
                                    {{ order.get_formatted_total() }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        {{ order.get_status_text('ar') }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                    <div class="flex justify-center space-x-2 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
                                        <a href="{{ url_for('admin.pos_process_payment', order_id=order.id) }}"
                                           class="bg-green-500 text-white px-3 py-1 rounded text-xs hover:bg-green-600 transition-colors">
                                            <i class="fas fa-credit-card {{ 'ml-1' if current_lang == 'ar' else 'mr-1' }}"></i>
                                            دفع
                                        </a>
                                        <a href="{{ url_for('admin.order_detail', id=order.id) }}"
                                           class="bg-blue-500 text-white px-3 py-1 rounded text-xs hover:bg-blue-600 transition-colors">
                                            <i class="fas fa-eye {{ 'ml-1' if current_lang == 'ar' else 'mr-1' }}"></i>
                                            عرض
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="p-6 text-center text-gray-500">
                <i class="fas fa-check-circle text-4xl mb-4"></i>
                <p>لا توجد طلبات معلقة للدفع</p>
            </div>
        {% endif %}
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <a href="{{ url_for('admin.pos_cash_transaction') }}" class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-exchange-alt text-blue-600 text-2xl"></i>
                </div>
                <div class="{{ 'mr-4' if current_lang == 'ar' else 'ml-4' }}">
                    <h3 class="text-lg font-medium text-gray-900">معاملة نقدية</h3>
                    <p class="text-sm text-gray-500">إيداع أو سحب نقدي</p>
                </div>
            </div>
        </a>

        <a href="{{ url_for('admin.pos_shifts_list') }}" class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-history text-purple-600 text-2xl"></i>
                </div>
                <div class="{{ 'mr-4' if current_lang == 'ar' else 'ml-4' }}">
                    <h3 class="text-lg font-medium text-gray-900">تاريخ الورديات</h3>
                    <p class="text-sm text-gray-500">عرض الورديات السابقة</p>
                </div>
            </div>
        </a>

        <a href="{{ url_for('admin.orders') }}" class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-list text-green-600 text-2xl"></i>
                </div>
                <div class="{{ 'mr-4' if current_lang == 'ar' else 'ml-4' }}">
                    <h3 class="text-lg font-medium text-gray-900">جميع الطلبات</h3>
                    <p class="text-sm text-gray-500">إدارة الطلبات</p>
                </div>
            </div>
        </a>
    </div>
</div>
{% endblock %}
