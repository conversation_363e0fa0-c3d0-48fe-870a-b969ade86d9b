{% extends "admin/base.html" %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">
            <i class="fas fa-edit {{ 'ml-3' if current_lang == 'ar' else 'mr-3' }}"></i>
            {{ title }}
        </h1>
        <div class="flex space-x-2 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
            <a href="{{ url_for('admin.order_detail', id=order.id) }}" class="border border-blue-500 text-blue-500 px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors">
                <i class="fas fa-eye {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                عرض التفاصيل
            </a>
            <a href="{{ url_for('admin.orders') }}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                <i class="fas fa-arrow-left {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                العودة للطلبات
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-2">
            <!-- Order Form -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-edit {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                        تعديل معلومات الطلب
                    </h3>
                </div>
                <div class="p-6">
                    <form method="POST">
                        {{ form.hidden_tag() }}

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Customer Name -->
                            <div>
                                {{ form.customer_name.label(class="block text-sm font-medium text-gray-700 mb-2") }}
                                {{ form.customer_name(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" + (" border-red-500" if form.customer_name.errors else "")) }}
                                {% if form.customer_name.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {% for error in form.customer_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Customer Phone -->
                            <div>
                                {{ form.customer_phone.label(class="block text-sm font-medium text-gray-700 mb-2") }}
                                {{ form.customer_phone(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" + (" border-red-500" if form.customer_phone.errors else "")) }}
                                {% if form.customer_phone.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {% for error in form.customer_phone.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <!-- Table -->
                            <div>
                                {{ form.table_id.label(class="block text-sm font-medium text-gray-700 mb-2") }}
                                {{ form.table_id(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" + (" border-red-500" if form.table_id.errors else "")) }}
                                {% if form.table_id.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {% for error in form.table_id.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Status -->
                            <div>
                                {{ form.status.label(class="block text-sm font-medium text-gray-700 mb-2") }}
                                {{ form.status(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" + (" border-red-500" if form.status.errors else "")) }}
                                {% if form.status.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {% for error in form.status.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="mt-1 text-sm text-gray-500">
                                    تغيير الحالة سيؤثر على حالة الطاولة المرتبطة
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mt-6">
                            {{ form.notes.label(class="block text-sm font-medium text-gray-700 mb-2") }}
                            {{ form.notes(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" + (" border-red-500" if form.notes.errors else "")) }}
                            {% if form.notes.errors %}
                                <div class="mt-1 text-sm text-red-600">
                                    {% for error in form.notes.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex flex-col sm:flex-row gap-3 justify-end mt-6">
                            <a href="{{ url_for('admin.order_detail', id=order.id) }}" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors text-center">
                                <i class="fas fa-times {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                <i class="fas fa-save {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="space-y-6">
            <!-- Order Summary -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-info-circle {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                        ملخص الطلب
                    </h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        <div><span class="font-medium text-gray-700">رقم الطلب:</span> {{ order.order_number }}</div>
                        <div><span class="font-medium text-gray-700">تاريخ الإنشاء:</span> {{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                        <div><span class="font-medium text-gray-700">المجموع:</span> <span class="text-green-600 font-bold">{{ order.get_formatted_total() }}</span></div>
                        <div><span class="font-medium text-gray-700">عدد العناصر:</span> {{ order.order_items | length }}</div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-utensils {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                        عناصر الطلب
                    </h3>
                </div>
                <div class="p-6">
                    {% for item in order.order_items %}
                        <div class="flex justify-between items-center py-3 border-b border-gray-200 last:border-b-0">
                            <div>
                                <div class="font-medium text-gray-900">{{ item.product.get_name('ar') }}</div>
                                <div class="text-sm text-gray-500">{{ item.quantity }} × {{ item.unit_price }} {{ order.currency }}</div>
                            </div>
                            <div class="font-bold text-gray-900">
                                {{ item.total_price }} {{ order.currency }}
                            </div>
                        </div>
                    {% endfor %}
                    <div class="flex justify-between items-center pt-4 mt-4 border-t border-gray-200">
                        <span class="font-bold text-gray-900">المجموع الكلي:</span>
                        <span class="font-bold text-green-600 text-lg">{{ order.get_formatted_total() }}</span>
                    </div>
                </div>
            </div>

            <!-- Current Table -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-chair {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                        الطاولة الحالية
                    </h3>
                </div>
                <div class="p-6 text-center">
                    <div class="border-2 rounded-lg
                        {% if order.table.get_status_color() == 'green' %}border-green-500
                        {% elif order.table.get_status_color() == 'red' %}border-red-500
                        {% elif order.table.get_status_color() == 'yellow' %}border-yellow-500
                        {% else %}border-gray-500{% endif %}">
                        <div class="px-4 py-3 text-white
                            {% if order.table.get_status_color() == 'green' %}bg-green-500
                            {% elif order.table.get_status_color() == 'red' %}bg-red-500
                            {% elif order.table.get_status_color() == 'yellow' %}bg-yellow-500
                            {% else %}bg-gray-500{% endif %}">
                            <h4 class="font-semibold">
                                طاولة {{ order.table.table_number }}
                            </h4>
                        </div>
                        <div class="p-4">
                            <span class="inline-flex px-3 py-1 rounded-full text-sm font-medium
                                {% if order.table.get_status_color() == 'green' %}bg-green-100 text-green-800
                                {% elif order.table.get_status_color() == 'red' %}bg-red-100 text-red-800
                                {% elif order.table.get_status_color() == 'yellow' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ order.table.get_status_text('ar') }}
                            </span>
                            <div class="mt-2 text-sm text-gray-600">
                                <i class="fas fa-users {{ 'ml-1' if current_lang == 'ar' else 'mr-1' }}"></i>
                                {{ order.table.capacity }} أشخاص
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}
