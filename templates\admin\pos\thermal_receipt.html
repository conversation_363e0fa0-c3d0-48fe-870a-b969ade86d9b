<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إيصال - {{ payment.payment_number }}</title>
    <style>
        @media print {
            body {
                margin: 0;
                padding: 0;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.2;
                width: 58mm;
                color: #000;
                background: #fff;
            }
            
            .receipt {
                width: 100%;
                padding: 2mm;
                box-sizing: border-box;
            }
            
            .header {
                text-align: center;
                margin-bottom: 3mm;
                border-bottom: 1px dashed #000;
                padding-bottom: 2mm;
            }
            
            .shop-name {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 1mm;
            }
            
            .shop-info {
                font-size: 10px;
                margin-bottom: 1mm;
            }
            
            .receipt-info {
                margin-bottom: 3mm;
                font-size: 10px;
            }
            
            .items {
                margin-bottom: 3mm;
                border-bottom: 1px dashed #000;
                padding-bottom: 2mm;
            }
            
            .item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 1mm;
                font-size: 10px;
            }
            
            .item-name {
                flex: 1;
                padding-right: 2mm;
            }
            
            .item-qty {
                width: 8mm;
                text-align: center;
            }
            
            .item-price {
                width: 15mm;
                text-align: left;
            }
            
            .totals {
                margin-bottom: 3mm;
                font-size: 11px;
            }
            
            .total-line {
                display: flex;
                justify-content: space-between;
                margin-bottom: 1mm;
            }
            
            .grand-total {
                font-weight: bold;
                font-size: 12px;
                border-top: 1px solid #000;
                padding-top: 1mm;
            }
            
            .payment-info {
                margin-bottom: 3mm;
                font-size: 10px;
                border-bottom: 1px dashed #000;
                padding-bottom: 2mm;
            }
            
            .footer {
                text-align: center;
                font-size: 9px;
                margin-top: 3mm;
            }
            
            .no-print {
                display: none;
            }
        }
        
        @media screen {
            body {
                font-family: 'Courier New', monospace;
                background: #f5f5f5;
                padding: 20px;
                direction: rtl;
            }
            
            .receipt {
                width: 58mm;
                margin: 0 auto;
                background: white;
                padding: 10px;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                border-radius: 5px;
            }
            
            .print-button {
                position: fixed;
                top: 20px;
                right: 20px;
                background: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
            }
            
            .print-button:hover {
                background: #0056b3;
            }
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">
        <i class="fas fa-print"></i> طباعة الإيصال
    </button>
    
    <div class="receipt">
        <!-- Header -->
        <div class="header">
            <div class="shop-name">{{ settings.shop_name }}</div>
            <div class="shop-info">
                {% if settings.phone %}{{ settings.phone }}<br>{% endif %}
                {% if settings.address %}{{ settings.address }}<br>{% endif %}
                {% if settings.tax_number %}ض.ب: {{ settings.tax_number }}{% endif %}
            </div>
        </div>
        
        <!-- Receipt Info -->
        <div class="receipt-info">
            <div>إيصال دفع رقم: {{ payment.payment_number }}</div>
            <div>طلب رقم: {{ payment.order.order_number }}</div>
            <div>التاريخ: {{ payment.payment_timestamp.strftime('%Y-%m-%d') }}</div>
            <div>الوقت: {{ payment.payment_timestamp.strftime('%H:%M:%S') }}</div>
            <div>الكاشير: {{ payment.processed_by.username }}</div>
            <div>العميل: {{ payment.order.customer_name }}</div>
            <div>طاولة: {{ payment.order.table.table_number }}</div>
        </div>
        
        <!-- Items -->
        <div class="items">
            <div class="item" style="font-weight: bold; border-bottom: 1px solid #000; margin-bottom: 2mm; padding-bottom: 1mm;">
                <div class="item-name">الصنف</div>
                <div class="item-qty">الكمية</div>
                <div class="item-price">السعر</div>
            </div>
            
            {% for item in payment.order.order_items %}
                <div class="item">
                    <div class="item-name">{{ item.product.get_name('ar') }}</div>
                    <div class="item-qty">{{ item.quantity }}</div>
                    <div class="item-price">{{ item.total_price }}</div>
                </div>
            {% endfor %}
        </div>
        
        <!-- Totals -->
        <div class="totals">
            <div class="total-line">
                <span>المجموع الفرعي:</span>
                <span>{{ payment.order.total_amount }} {{ payment.order.currency }}</span>
            </div>
            
            {% if payment.order.tax_amount and payment.order.tax_amount > 0 %}
                <div class="total-line">
                    <span>الضريبة:</span>
                    <span>{{ payment.order.tax_amount }} {{ payment.order.currency }}</span>
                </div>
            {% endif %}
            
            <div class="total-line grand-total">
                <span>المجموع الكلي:</span>
                <span>{{ payment.order.total_amount }} {{ payment.order.currency }}</span>
            </div>
        </div>
        
        <!-- Payment Info -->
        <div class="payment-info">
            <div>طريقة الدفع: {{ payment.get_payment_method_text('ar') }}</div>
            <div>المبلغ المدفوع: {{ payment.get_formatted_amount() }}</div>
            {% if payment.reference_number %}
                <div>رقم المرجع: {{ payment.reference_number }}</div>
            {% endif %}
            <div>حالة الدفع: {{ payment.get_status_text('ar') }}</div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div>شكراً لزيارتكم</div>
            <div>نتطلع لخدمتكم مرة أخرى</div>
            <div style="margin-top: 2mm;">
                ═══════════════════════
            </div>
            <div style="font-size: 8px; margin-top: 1mm;">
                تم الطباعة في: {{ moment().format('YYYY-MM-DD HH:mm:ss') }}
            </div>
        </div>
    </div>
    
    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() {
        //     window.print();
        // };
        
        // Close window after printing
        window.onafterprint = function() {
            // window.close();
        };
    </script>
</body>
</html>
