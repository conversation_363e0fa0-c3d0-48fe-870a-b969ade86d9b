{% extends "base.html" %}

{% block title %}{{ _('تأكيد الطلب') }} - {{ super() }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- رسالة النجاح - Success Message -->
    <div class="text-center mb-8">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
            <i class="fas fa-paper-plane text-2xl text-blue-600"></i>
        </div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">
            {{ _('تم إرسال طلبكم بنجاح!') }}
        </h1>
        <p class="text-gray-600">
            {{ _('شكراً لكم، تم إرسال طلبكم للإدارة وسيتم مراجعته وتأكيده قريباً') }}
        </p>
        <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg max-w-2xl mx-auto">
            <div class="flex items-center justify-center">
                <i class="fas fa-clock text-yellow-600 {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                <p class="text-yellow-800 text-sm">
                    {{ _('الطلب في انتظار موافقة الإدارة. سيتم إعلامكم بحالة الطلب قريباً') }}
                </p>
            </div>
        </div>
    </div>

    <!-- تفاصيل الطلب - Order Details -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <!-- رأس الطلب - Order Header -->
        <div class="bg-primary text-white px-6 py-4">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="text-xl font-semibold">{{ _('طلب رقم') }}: {{ order.order_number }}</h2>
                    <p class="text-primary-100">{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                </div>
                <div class="text-{{ 'right' if current_lang == 'ar' else 'left' }}">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                        <i class="fas fa-clock {{ 'ml-1' if current_lang == 'ar' else 'mr-1' }}"></i>
                        {{ order.get_status_text(current_lang) }}
                    </span>
                </div>
            </div>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <!-- معلومات العميل - Customer Information -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">
                        <i class="fas fa-user {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }} text-primary"></i>
                        {{ _('معلومات العميل') }}
                    </h3>
                    <div class="space-y-2 text-gray-600">
                        <p><strong>{{ _('الاسم') }}:</strong> {{ order.customer_name }}</p>
                        <p><strong>{{ _('الهاتف') }}:</strong> {{ order.customer_phone }}</p>
                    </div>
                </div>

                <!-- معلومات الطاولة - Table Information -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">
                        <i class="fas fa-chair {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }} text-primary"></i>
                        {{ _('معلومات الطاولة') }}
                    </h3>
                    <div class="space-y-2 text-gray-600">
                        <p><strong>{{ _('رقم الطاولة') }}:</strong> {{ order.table.table_number }}</p>
                        <p><strong>{{ _('السعة') }}:</strong> {{ order.table.capacity }} {{ _('أشخاص') }}</p>
                    </div>
                </div>
            </div>

            <!-- عناصر الطلب - Order Items -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-utensils {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }} text-primary"></i>
                    {{ _('عناصر الطلب') }}
                </h3>
                
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 {{ 'text-right' if current_lang == 'ar' else 'text-left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {{ _('المنتج') }}
                                </th>
                                <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {{ _('الكمية') }}
                                </th>
                                <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {{ _('سعر الوحدة') }}
                                </th>
                                <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {{ _('المجموع') }}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for item in order.order_items %}
                                <tr>
                                    <td class="px-4 py-4">
                                        <div class="flex items-center space-x-3 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
                                            {% if item.product.photo_b64 %}
                                                <img src="{{ item.product.photo_b64 }}" 
                                                     alt="{{ item.product.get_name(current_lang) }}"
                                                     class="w-10 h-10 object-cover rounded-lg">
                                            {% else %}
                                                <div class="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                                                    <i class="fas fa-utensils text-gray-400 text-sm"></i>
                                                </div>
                                            {% endif %}
                                            <div>
                                                <p class="text-sm font-medium text-gray-900">
                                                    {{ item.product.get_name(current_lang) }}
                                                </p>
                                                {% if item.notes %}
                                                    <p class="text-xs text-gray-500">{{ item.notes }}</p>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-4 text-center text-sm text-gray-900">
                                        {{ item.quantity }}
                                    </td>
                                    <td class="px-4 py-4 text-center text-sm text-gray-900">
                                        {{ item.unit_price }} {{ order.currency }}
                                    </td>
                                    <td class="px-4 py-4 text-center text-sm font-medium text-gray-900">
                                        {{ item.total_price }} {{ order.currency }}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- ملاحظات - Notes -->
            {% if order.notes %}
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">
                        <i class="fas fa-sticky-note {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }} text-primary"></i>
                        {{ _('ملاحظات') }}
                    </h3>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-700">{{ order.notes }}</p>
                    </div>
                </div>
            {% endif %}

            <!-- المجموع الكلي - Total -->
            <div class="border-t border-gray-200 pt-4">
                <div class="flex justify-between items-center">
                    <span class="text-lg font-semibold text-gray-900">{{ _('المجموع الكلي') }}</span>
                    <span class="text-2xl font-bold text-primary">{{ order.get_formatted_total() }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- الإجراءات التالية - Next Actions -->
    <div class="mt-8 bg-blue-50 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-blue-900 mb-3">
            <i class="fas fa-info-circle {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
            {{ _('ماذا بعد؟') }}
        </h3>
        <div class="space-y-2 text-blue-800">
            <p>• {{ _('ستقوم الإدارة بمراجعة طلبكم وتأكيده') }}</p>
            <p>• {{ _('سيتم إعلامكم بحالة الطلب (مقبول/مرفوض)') }}</p>
            <p>• {{ _('بعد الموافقة، سيتم تحضير طلبكم في المطبخ') }}</p>
            <p>• {{ _('يمكنكم الجلوس في الطاولة المحددة بعد تأكيد الطلب') }}</p>
            <p>• {{ _('الدفع سيتم عند الاستلام أو حسب سياسة المطعم') }}</p>
            <p>• {{ _('في حالة وجود أي استفسار، يرجى التواصل مع الموظفين') }}</p>
        </div>
    </div>

    <!-- أزرار الإجراءات - Action Buttons -->
    <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
        <a href="{{ url_for('core.menu') }}" 
           class="bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 transition-colors text-center">
            <i class="fas fa-utensils {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
            {{ _('طلب جديد') }}
        </a>
        <a href="{{ url_for('core.index') }}" 
           class="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors text-center">
            <i class="fas fa-home {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
            {{ _('العودة للرئيسية') }}
        </a>
    </div>
</div>
{% endblock %}
