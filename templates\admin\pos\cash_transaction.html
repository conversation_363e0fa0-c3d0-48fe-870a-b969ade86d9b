{% extends "admin/base.html" %}

{% block title %}معاملة نقدية - {{ super() }}{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">
            <i class="fas fa-exchange-alt {{ 'ml-3' if current_lang == 'ar' else 'mr-3' }}"></i>
            معاملة نقدية جديدة
        </h1>
        <a href="{{ url_for('admin.pos_cash_management') }}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
            <i class="fas fa-arrow-left {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
            العودة
        </a>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-blue-500 text-white px-6 py-4">
            <h3 class="text-lg font-semibold">
                <i class="fas fa-money-bill-wave {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                تفاصيل المعاملة
            </h3>
        </div>
        
        <div class="p-6">
            <form method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                
                <!-- Transaction Type -->
                <div class="mb-6">
                    <label for="transaction_type" class="block text-sm font-medium text-gray-700 mb-2">
                        نوع المعاملة <span class="text-red-500">*</span>
                    </label>
                    {{ form.transaction_type(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent", id="transaction_type") }}
                    {% if form.transaction_type.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.transaction_type.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Amount -->
                <div class="mb-6">
                    <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
                        المبلغ <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        {{ form.amount(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent", id="amount", placeholder="0.00") }}
                        <div class="absolute inset-y-0 {{ 'left-0' if current_lang == 'ar' else 'right-0' }} pr-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">{{ settings.default_currency }}</span>
                        </div>
                    </div>
                    {% if form.amount.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.amount.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Description -->
                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        الوصف <span class="text-red-500">*</span>
                    </label>
                    {{ form.description(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent", id="description", placeholder="وصف المعاملة") }}
                    {% if form.description.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.description.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Notes -->
                <div class="mb-6">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                        ملاحظات (اختياري)
                    </label>
                    {{ form.notes(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent", id="notes", placeholder="ملاحظات إضافية") }}
                    {% if form.notes.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.notes.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Submit Button -->
                <div class="flex space-x-4 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
                    <button type="submit" 
                            class="flex-1 bg-blue-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <i class="fas fa-check {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                        تأكيد المعاملة
                    </button>
                    
                    <a href="{{ url_for('admin.pos_cash_management') }}" 
                       class="flex-1 bg-gray-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-gray-600 transition-colors text-center">
                        <i class="fas fa-times {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                        إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Warning Notice -->
    <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-600"></i>
            </div>
            <div class="{{ 'mr-3' if current_lang == 'ar' else 'ml-3' }}">
                <h3 class="text-sm font-medium text-yellow-800">تنبيه مهم</h3>
                <div class="mt-2 text-sm text-yellow-700">
                    <ul class="list-disc {{ 'pr-5' if current_lang == 'ar' else 'pl-5' }} space-y-1">
                        <li>تأكد من صحة المبلغ قبل التأكيد</li>
                        <li>سيتم تحديث رصيد الخزنة فوراً</li>
                        <li>لا يمكن التراجع عن هذه المعاملة بعد التأكيد</li>
                        <li>تأكد من وجود المبلغ فعلياً في الخزنة عند الإيداع</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-select transaction type from URL parameter
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const type = urlParams.get('type');
    
    if (type && ['deposit', 'withdrawal'].includes(type)) {
        const select = document.getElementById('transaction_type');
        select.value = type;
    }
});

// Update button color based on transaction type
document.getElementById('transaction_type').addEventListener('change', function() {
    const submitButton = document.querySelector('button[type="submit"]');
    const value = this.value;
    
    // Reset classes
    submitButton.className = submitButton.className.replace(/bg-(blue|green|red)-\d+/g, '');
    submitButton.className = submitButton.className.replace(/hover:bg-(blue|green|red)-\d+/g, '');
    submitButton.className = submitButton.className.replace(/focus:ring-(blue|green|red)-\d+/g, '');
    
    // Add appropriate color
    if (value === 'deposit') {
        submitButton.classList.add('bg-green-500', 'hover:bg-green-600', 'focus:ring-green-500');
    } else if (value === 'withdrawal') {
        submitButton.classList.add('bg-red-500', 'hover:bg-red-600', 'focus:ring-red-500');
    } else {
        submitButton.classList.add('bg-blue-500', 'hover:bg-blue-600', 'focus:ring-blue-500');
    }
});
</script>
{% endblock %}
